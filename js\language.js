/**
 * 語言切換功能
 * KMS PC Receipt Maker
 */

// 語言數據
const languages = {
    zh: {
        // 應用標題和導航
        app_title: 'KMS 收據生成器',
        nav_create: '創建收據',
        nav_history: '歷史記錄',
        language: '語言',

        // 創建收據頁面
        create_receipt: '創建新收據',
        customer_info: '客戶信息',
        customer_name: '客戶姓名',
        customer_phone: '聯絡電話',
        customer_email: '電子郵件',
        customer_address: '地址',
        payment_method: '付款方式',
        payment_cash: '現金',
        payment_card: '信用卡',
        payment_transfer: '轉帳',
        payment_other: '其他',

        // 收據項目
        receipt_items: '收據項目',
        add_item: '添加項目',
        item_name: '項目名稱',
        item_description: '項目描述',
        item_category: '分類',
        quantity: '數量',
        unit_price: '單價',
        total_price: '總價',
        remove_item: '移除項目',
        select_preset: '選擇預設',
        confirm_add: '確認添加',
        clear: '清空',
        no_items: '尚未添加任何項目',
        receipt_info: '收據信息',
        service: '服務',

        // 總計
        totals: '總計',
        subtotal: '小計',
        discount: '折扣金額',
        tax: '稅費',
        total: '總計',
        notes: '備註',

        // 操作按鈕
        generate_receipt: '生成收據',
        clear_form: '清空表單',
        save_receipt: '保存收據',
        print_receipt: '打印收據',
        close: '關閉',

        // 預覽和歷史
        receipt_preview: '收據預覽',
        preview_placeholder: '收據預覽將在這裡顯示',
        receipt_history: '收據歷史',
        receipt_details: '收據詳情',
        search_placeholder: '搜索收據...',

        // 收據內容
        receipt_number: '收據編號',
        receipt_date: '開立日期',

        // 表格標題
        item: '項目',
        description: '描述',
        category: '分類',
        qty: '數量',
        price: '單價',
        amount: '金額',

        // 狀態和訊息
        paid: '已付款',
        pending: '待付款',
        cancelled: '已取消',
        thank_you: '感謝您的惠顧！',
        seller_signature: '賣方簽名',
        buyer_signature: '買方簽名',
        signature_date: '簽名日期',

        // 錯誤訊息
        error_customer_name: '請輸入客戶姓名',
        error_no_items: '請至少添加一個項目',
        error_invalid_quantity: '請輸入有效的數量',
        error_invalid_price: '請輸入有效的價格',
        success_receipt_saved: '收據保存成功',
        success_receipt_generated: '收據生成成功',
        success_item_added: '項目添加成功',
        success_item_updated: '項目更新成功',
        success_item_removed: '項目已刪除',
        edit_item: '編輯項目',
        save_changes: '保存更改',
        enter_config_name: '請輸入配置名稱',
        config_saved_success: '配置保存成功！',
        config_loaded_success: '配置載入成功！',
        config_deleted_success: '配置刪除成功！',
        config_not_found: '找不到指定的配置',
        confirm_delete_config: '確定要刪除此配置嗎？',
        save_configuration: '保存配置',
        load_configuration: '載入配置',
        manage_configurations: '管理配置',
        add_preset: '新增預設',
        edit_preset: '編輯預設',
        delete_preset: '刪除預設',
        save: '保存',
        cancel: '取消',
        move_up: '上移',
        move_down: '下移',
        confirm_delete_preset: '確定要刪除此預設項目嗎？',
        preset_saved_success: '預設項目保存成功！',
        preset_deleted_success: '預設項目刪除成功！',
        original_price: '原價',
        special_price: '特價',
        discount_percentage: '折扣百分比',
        hide_price: '隱藏價格 (收據上顯示 N/A)',
        update: '更新',
        add_item_description: '點擊下方按鈕添加項目到收據中',
        upload_logo: '上傳 Logo',
        logo_upload_hint: '支持 JPG, PNG, GIF 格式，無檔案大小限制',
        error_invalid_image: '請選擇有效的圖片檔案',
        logo_uploaded_success: 'Logo 上傳成功！',
        logo_preview: 'Logo 預覽',
        logo_info: '圖片資訊',
        file_name: '檔案名稱',
        file_type: '檔案類型',
        file_size: '檔案大小',
        image_dimensions: '圖片尺寸',
        remove_logo: '移除 Logo',
        logo_removed: 'Logo 已移除',
        tax_rate: '稅率',
        clear_all: '清空全部',
        confirm_clear_all: '確定要清空所有項目嗎？',
        all_items_cleared: '所有項目已清空',

        // 登入相關
        login_title: 'KMS 收據生成器',
        login_subtitle: '請登入以繼續',
        username: '用戶名',
        password: '密碼',
        sign_in: '登入',
        logout: '登出',
        welcome: '歡迎',

        // 分類
        cpu: 'CPU 處理器',
        motherboard: '主機板',
        memory: '記憶體',
        graphics_card: '顯示卡',
        storage: '儲存裝置',
        power_supply: '電源供應器',
        case: '機殼',
        cooler: '散熱器',
        other: '其他',

        // 搜索和篩選
        search_receipts: '搜索收據...',
        search_items: '搜索項目...',
        all_payment_methods: '所有付款方式',
        all_categories: '所有分類',

        // 公司信息
        company_name: 'KelvinKMS',
        company_website: 'KelvinKMS.com',
        company_phone: '************',
        company_email: '<EMAIL>',

        // 錯誤和警告訊息
        error_no_items_to_print: '請先添加項目到收據中',
        error_no_items_to_save: '請先添加項目到收據中',
        error_no_items_to_generate: '請先添加項目到收據中',
        confirm_clear_form: '確定要清空表單嗎？',
        form_cleared: '表單已清空',
        preset_feature_unavailable: '預設項目功能暫時無法使用'
    },

    en: {
        // Application title and navigation
        app_title: 'KMS Receipt Maker',
        nav_create: 'Create Receipt',
        nav_history: 'History',
        language: 'Language',

        // Create receipt page
        create_receipt: 'Create New Receipt',
        customer_info: 'Customer Info.',
        customer_name: 'Customer Name',
        customer_phone: 'Phone Number',
        customer_email: 'Email Address',
        customer_address: 'Address',
        payment_method: 'Payment Method',
        payment_cash: 'Cash',
        payment_card: 'Credit Card',
        payment_transfer: 'Bank Transfer',
        payment_other: 'Other',

        // Receipt items
        receipt_items: 'Receipt Items',
        add_item: 'Add Item',
        item_name: 'Item Name',
        item_description: 'Item Description',
        item_category: 'Category',
        quantity: 'Quantity',
        unit_price: 'Unit Price',
        total_price: 'Total Price',
        remove_item: 'Remove Item',
        select_preset: 'Select Preset',
        confirm_add: 'Confirm Add',
        clear: 'Clear',
        no_items: 'No items added yet',
        receipt_info: 'Receipt Information',
        service: 'Service',

        // Totals
        totals: 'Totals',
        subtotal: 'Subtotal',
        discount: 'Discount Amount',
        tax: 'Tax',
        total: 'Total',
        notes: 'Notes',

        // Action buttons
        generate_receipt: 'Generate Receipt',
        clear_form: 'Clear Form',
        save_receipt: 'Save Receipt',
        print_receipt: 'Print Receipt',
        close: 'Close',

        // Preview and history
        receipt_preview: 'Receipt Preview',
        preview_placeholder: 'Receipt preview will be displayed here',
        receipt_history: 'Receipt History',
        receipt_details: 'Receipt Details',
        search_placeholder: 'Search receipts...',

        // Receipt content
        receipt_number: 'Receipt Number',
        receipt_date: 'Date Issued',

        // Table headers
        item: 'Item',
        description: 'Description',
        category: 'Category',
        qty: 'Qty',
        price: 'Price',
        amount: 'Amount',

        // Status and messages
        paid: 'Paid',
        pending: 'Pending',
        cancelled: 'Cancelled',
        thank_you: 'Thank you for your business!',
        seller_signature: 'Seller Signature',
        buyer_signature: 'Buyer Signature',
        signature_date: 'Signature Date',

        // Error messages
        error_customer_name: 'Please enter customer name',
        error_no_items: 'Please add at least one item',
        error_invalid_quantity: 'Please enter a valid quantity',
        error_invalid_price: 'Please enter a valid price',
        success_receipt_saved: 'Receipt saved successfully',
        success_receipt_generated: 'Receipt generated successfully',
        success_item_added: 'Item added successfully',
        success_item_updated: 'Item updated successfully',
        success_item_removed: 'Item removed',
        edit_item: 'Edit Item',
        save_changes: 'Save Changes',
        enter_config_name: 'Please enter configuration name',
        config_saved_success: 'Configuration saved successfully!',
        config_loaded_success: 'Configuration loaded successfully!',
        config_deleted_success: 'Configuration deleted successfully!',
        config_not_found: 'Configuration not found',
        confirm_delete_config: 'Are you sure you want to delete this configuration?',
        save_configuration: 'Save Configuration',
        load_configuration: 'Load Configuration',
        manage_configurations: 'Manage Configurations',
        add_preset: 'Add Preset',
        edit_preset: 'Edit Preset',
        delete_preset: 'Delete Preset',
        save: 'Save',
        cancel: 'Cancel',
        move_up: 'Move Up',
        move_down: 'Move Down',
        confirm_delete_preset: 'Are you sure you want to delete this preset item?',
        preset_saved_success: 'Preset item saved successfully!',
        preset_deleted_success: 'Preset item deleted successfully!',
        original_price: 'Original Price',
        special_price: 'Special Price',
        discount_percentage: 'Discount Percentage',
        hide_price: 'Hide Price (Show N/A on receipt)',
        update: 'Update',
        add_item_description: 'Click the button below to add items to the receipt',
        upload_logo: 'Upload Logo',
        logo_upload_hint: 'Supports JPG, PNG, GIF formats, no file size limit',
        error_invalid_image: 'Please select a valid image file',
        logo_uploaded_success: 'Logo uploaded successfully!',
        logo_preview: 'Logo Preview',
        logo_info: 'Image Information',
        file_name: 'File Name',
        file_type: 'File Type',
        file_size: 'File Size',
        image_dimensions: 'Image Dimensions',
        remove_logo: 'Remove Logo',
        logo_removed: 'Logo removed',
        tax_rate: 'Tax Rate',
        clear_all: 'Clear All',
        confirm_clear_all: 'Are you sure you want to clear all items?',
        all_items_cleared: 'All items cleared',

        // Authentication
        login_title: 'KMS Receipt Maker',
        login_subtitle: 'Please sign in to continue',
        username: 'Username',
        password: 'Password',
        sign_in: 'Sign In',
        logout: 'Logout',
        welcome: 'Welcome',

        // Categories
        cpu: 'CPU Processor',
        motherboard: 'Motherboard',
        memory: 'Memory',
        graphics_card: 'Graphics Card',
        storage: 'Storage',
        power_supply: 'Power Supply',
        case: 'Case',
        cooler: 'Cooler',
        other: 'Other',

        // Search and filters
        search_receipts: 'Search receipts...',
        search_items: 'Search items...',
        all_payment_methods: 'All payment methods',
        all_categories: 'All categories',

        // Company information
        company_name: 'KelvinKMS',
        company_website: 'KelvinKMS.com',
        company_phone: '************',
        company_email: '<EMAIL>',

        // Error and warning messages
        error_no_items_to_print: 'Please add items to the receipt first',
        error_no_items_to_save: 'Please add items to the receipt first',
        error_no_items_to_generate: 'Please add items to the receipt first',
        confirm_clear_form: 'Are you sure you want to clear the form?',
        form_cleared: 'Form cleared',
        preset_feature_unavailable: 'Preset items feature is temporarily unavailable'
    }
};

// 當前語言
let currentLanguage = localStorage.getItem('language') || 'zh';

// 系統配置
let systemConfig = {};

/**
 * 載入系統配置
 */
async function loadSystemConfig() {
    try {
        const response = await fetch('php/get_config.php');
        const result = await response.json();

        if (result.success) {
            systemConfig = result.data;
        } else {
            console.error('Failed to load system config:', result.message);
        }
    } catch (error) {
        console.error('Error loading system config:', error);
    }
}

/**
 * 初始化語言設置
 */
async function initializeLanguage() {
    // 載入系統配置
    await loadSystemConfig();

    // 設置HTML語言屬性
    document.documentElement.lang = currentLanguage === 'zh' ? 'zh-TW' : 'en';

    // 應用語言
    applyLanguage(currentLanguage);

    // 更新語言選擇器
    updateLanguageSelector();
}

/**
 * 切換語言
 */
function changeLanguage(lang) {
    if (lang && languages[lang]) {
        currentLanguage = lang;
        localStorage.setItem('language', lang);

        // 設置HTML語言屬性
        document.documentElement.lang = lang === 'zh' ? 'zh-TW' : 'en';

        // 應用語言
        applyLanguage(lang);

        // 更新語言選擇器
        updateLanguageSelector();

        // 觸發語言變更事件
        document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));

        // 立即重新應用語言到所有元素（包括動態內容）
        setTimeout(() => {
            applyLanguage(lang);
            // 更新模態框內容（如果存在）
            updateModalLanguage(lang);
        }, 100);
    }
}

/**
 * 應用語言到頁面元素
 */
function applyLanguage(lang, container = document) {
    const langData = languages[lang];

    // 使用容器版本的函數
    applyLanguageToContainer(lang, container);

    // 只在處理整個文檔時更新頁面標題
    if (container === document) {
        // 更新頁面標題
        if (langData.app_title) {
            document.title = langData.app_title;
        }

        // 更新搜索框佔位符
        const searchInput = document.getElementById('searchInput');
        if (searchInput && langData.search_placeholder) {
            searchInput.placeholder = langData.search_placeholder;
        }
    }
}

/**
 * 更新語言選擇器狀態
 */
function updateLanguageSelector() {
    // 這裡可以添加語言選擇器的視覺反饋
    console.log(`Language changed to: ${currentLanguage}`);
}

/**
 * 獲取當前語言的文本
 */
function getText(key) {
    return languages[currentLanguage][key] || key;
}

/**
 * 獲取當前語言
 */
function getCurrentLanguage() {
    return currentLanguage;
}

/**
 * 獲取系統配置
 */
function getSystemConfig(key) {
    return systemConfig[key] || '';
}

/**
 * 重新應用語言到指定容器或整個頁面
 */
function reapplyLanguage(container = document) {
    applyLanguage(currentLanguage, container);
}

/**
 * 應用語言到指定容器的元素
 */
function applyLanguageToContainer(lang, container = document) {
    const langData = languages[lang];

    // 更新容器內所有帶有 data-lang 屬性的元素
    container.querySelectorAll('[data-lang]').forEach(element => {
        const key = element.getAttribute('data-lang');
        if (langData[key]) {
            // 根據元素類型更新內容
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'submit' || element.type === 'button') {
                    element.value = langData[key];
                } else {
                    element.placeholder = langData[key];
                }
            } else if (element.tagName === 'OPTION') {
                element.textContent = langData[key];
            } else {
                element.textContent = langData[key];
            }
        }
    });
}

/**
 * 更新模態框語言內容
 */
function updateModalLanguage(lang) {
    // 更新預設項目模態框
    const presetModal = document.getElementById('presetModal');
    if (presetModal && presetModal.classList.contains('is-open')) {
        // 更新搜索輸入框佔位符
        const searchInput = document.getElementById('presetSearch');
        if (searchInput) {
            if (lang === 'en') {
                searchInput.placeholder = 'Search items...';
            } else {
                searchInput.placeholder = '搜索項目...';
            }
        }

        // 更新分類篩選器的第一個選項
        const categoryFilter = document.getElementById('presetCategoryFilter');
        if (categoryFilter && categoryFilter.options.length > 0) {
            if (lang === 'en') {
                categoryFilter.options[0].textContent = 'All categories';
            } else {
                categoryFilter.options[0].textContent = '所有分類';
            }
        }
    }

    // 可以在這裡添加其他模態框的語言更新
}

/**
 * 格式化數字為貨幣格式
 */
function formatCurrency(amount) {
    const formatter = new Intl.NumberFormat(currentLanguage === 'zh' ? 'zh-TW' : 'en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });

    return formatter.format(amount);
}

/**
 * 格式化日期
 */
function formatDate(date) {
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    };

    return new Intl.DateTimeFormat(currentLanguage === 'zh' ? 'zh-TW' : 'en-US', options).format(date);
}

// 頁面載入時初始化語言
document.addEventListener('DOMContentLoaded', initializeLanguage);

// 導出函數供其他模塊使用
window.LanguageManager = {
    changeLanguage,
    getText,
    getCurrentLanguage,
    getSystemConfig,
    formatCurrency,
    formatDate,
    reapplyLanguage,
    applyLanguageToContainer,
    updateModalLanguage
};
