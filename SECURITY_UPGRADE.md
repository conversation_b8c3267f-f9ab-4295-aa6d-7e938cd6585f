# 🔐 KMS Receipt Maker - 安全升級說明

## 安全漏洞修復

### ⚠️ 原有安全問題
1. **密碼明文儲存** - `auth/users.json` 中密碼以明文形式儲存
2. **文件直接訪問** - 任何人都可以通過瀏覽器直接訪問 `auth/users.json` 獲取所有帳號密碼
3. **無加密傳輸** - 登入過程中密碼可能以明文傳輸

### ✅ 新安全措施
1. **數據庫儲存** - 用戶資料現在安全地儲存在 MySQL 數據庫中
2. **密碼哈希** - 使用 PHP 的 `password_hash()` 函數進行安全哈希
3. **會話管理** - 使用安全的會話令牌系統
4. **API 保護** - 所有 API 都有適當的權限檢查
5. **文件保護** - 敏感文件被 `.htaccess` 保護
6. **SQL 注入防護** - 使用預處理語句防止 SQL 注入
7. **XSS 防護** - 輸出內容經過適當轉義

## 🚀 升級步驟

### 1. 數據庫設置
```bash
# 在瀏覽器中訪問或命令行執行
php php/setup_auth.php
```

### 2. 測試新系統
- 訪問 `login.html` 進行登入測試
- 使用預設帳號：`admin` / `admin123`
- 訪問 `admin.html` 進行用戶管理

### 3. 清理舊文件（可選）
```bash
# 刪除不安全的舊文件
rm auth/users.json
```

## 📁 新文件結構

```
KMS_Receipt_Maker.app/
├── auth/
│   └── .htaccess              # 保護 auth 目錄
├── database/
│   └── create_users_table.sql # 數據庫結構
├── php/
│   ├── AuthDatabase.php       # 認證數據庫類
│   ├── auth_login.php         # 登入 API
│   ├── auth_validate.php      # 會話驗證 API
│   ├── auth_logout.php        # 登出 API
│   ├── admin_users.php        # 用戶管理 API
│   └── setup_auth.php         # 初始化腳本
├── js/
│   ├── auth.js               # 更新的認證系統
│   └── admin.js              # 管理員頁面腳本
├── admin.html                # 管理員頁面
└── login.html                # 登入頁面
```

## 🛡️ 安全特性

### 密碼安全
- 使用 bcrypt 哈希算法
- 每個密碼都有唯一的鹽值
- 密碼永不以明文儲存或傳輸

### 會話安全
- 安全的會話令牌（32字節隨機）
- 會話自動過期（預設1小時）
- 登出時會話立即失效

### API 安全
- 所有 API 都需要有效會話
- 管理員 API 需要管理員權限
- 輸入驗證和清理
- 錯誤信息不洩露敏感資訊

### 暴力攻擊防護
- 登入失敗次數限制
- 帳號自動鎖定機制
- IP 基礎的速率限制

## 👥 用戶管理

### 預設帳號
| 用戶名 | 密碼 | 角色 | 說明 |
|--------|------|------|------|
| admin | admin123 | admin | 系統管理員 |
| user1 | password123 | user | 測試用戶 |
| kms | kms2024 | user | KMS 用戶 |

### 管理員功能
- 創建新用戶
- 編輯用戶資訊
- 重置用戶密碼
- 啟用/停用用戶
- 刪除用戶（保護最後一個管理員）

## 🔧 配置選項

### 數據庫配置
在 `php/config.php` 中設置：
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'kms_receipt_maker');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 安全設置
在 `auth_settings` 表中可調整：
- `session_timeout` - 會話超時時間（秒）
- `max_login_attempts` - 最大登入嘗試次數
- `lockout_duration` - 帳號鎖定時間（秒）
- `password_min_length` - 密碼最小長度

## 🚨 重要提醒

1. **立即更改預設密碼** - 部署後請立即更改所有預設密碼
2. **定期備份數據庫** - 用戶資料現在儲存在數據庫中
3. **使用 HTTPS** - 生產環境中務必使用 HTTPS
4. **定期更新** - 保持系統和依賴項目的更新
5. **監控日誌** - 定期檢查錯誤日誌和訪問日誌

## 📞 支援

如果遇到問題：
1. 檢查 PHP 錯誤日誌
2. 確認數據庫連接設置
3. 驗證文件權限
4. 檢查 `.htaccess` 配置

## 🎯 下一步建議

1. 實施雙因素認證（2FA）
2. 添加密碼複雜度要求
3. 實施更詳細的審計日誌
4. 添加 CAPTCHA 防護
5. 實施 IP 白名單功能
