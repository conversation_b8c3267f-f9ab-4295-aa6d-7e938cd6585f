<?php
/**
 * 用戶管理 API
 * KMS Receipt Maker - 管理員專用
 */

header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

try {
    require_once 'AuthDatabase.php';

    // 驗證管理員權限
    function requireAdmin() {
        $authDb = new AuthDatabase();
        
        // 獲取會話令牌
        $sessionToken = null;
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                $sessionToken = $matches[1];
            }
        }
        
        if (!$sessionToken && isset($_COOKIE['kms_session'])) {
            $sessionToken = $_COOKIE['kms_session'];
        }

        if (!$sessionToken) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Authentication required']);
            exit;
        }

        $user = $authDb->validateSession($sessionToken);
        if (!$user || $user['role'] !== 'admin') {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Admin access required']);
            exit;
        }

        return $user;
    }

    $admin = requireAdmin();
    $authDb = new AuthDatabase();
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // 獲取所有用戶列表
            $pdo = $authDb->getPdo();
            $stmt = $pdo->prepare("
                SELECT id, username, display_name,
                       CASE
                           WHEN user_type = 'admin' THEN 'admin'
                           ELSE 'user'
                       END as role,
                       CASE
                           WHEN status = 'active' THEN 1
                           ELSE 0
                       END as is_active,
                       created_at, last_login, login_attempts, locked_until
                FROM users
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            $users = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'users' => $users
            ]);
            break;

        case 'POST':
            // 創建新用戶
            $input = json_decode(file_get_contents('php://input'), true);
            
            $username = trim($input['username'] ?? '');
            $password = $input['password'] ?? '';
            $displayName = trim($input['display_name'] ?? '');
            $role = $input['role'] ?? 'user';

            // 驗證輸入
            if (empty($username) || empty($password)) {
                throw new Exception('Username and password are required');
            }

            if (strlen($username) < 3 || strlen($username) > 50) {
                throw new Exception('Username must be between 3 and 50 characters');
            }

            if (strlen($password) < 6) {
                throw new Exception('Password must be at least 6 characters');
            }

            if (!in_array($role, ['admin', 'user'])) {
                throw new Exception('Invalid role');
            }

            // 檢查用戶名是否已存在
            $pdo = $authDb->getPdo();
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                throw new Exception('Username already exists');
            }

            // 創建用戶
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            $userType = ($role === 'admin') ? 'admin' : 'member';
            $status = 'active';

            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, display_name, user_type, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$username, $username . '@example.com', $passwordHash, $displayName, $userType, $status]);

            echo json_encode([
                'success' => true,
                'message' => 'User created successfully',
                'user_id' => $pdo->lastInsertId()
            ]);
            break;

        case 'PUT':
            // 更新用戶
            $input = json_decode(file_get_contents('php://input'), true);
            
            $userId = $input['id'] ?? 0;
            $username = trim($input['username'] ?? '');
            $displayName = trim($input['display_name'] ?? '');
            $role = $input['role'] ?? 'user';
            $isActive = $input['is_active'] ?? 1;
            $password = $input['password'] ?? '';

            if (!$userId) {
                throw new Exception('User ID is required');
            }

            // 檢查用戶是否存在
            $pdo = $authDb->getPdo();
            $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $existingUser = $stmt->fetch();
            if (!$existingUser) {
                throw new Exception('User not found');
            }

            // 如果更改用戶名，檢查是否重複
            if ($username !== $existingUser['username']) {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
                $stmt->execute([$username, $userId]);
                if ($stmt->fetch()) {
                    throw new Exception('Username already exists');
                }
            }

            // 構建更新查詢
            $updateFields = [];
            $params = [];

            if (!empty($username)) {
                $updateFields[] = "username = ?";
                $params[] = $username;
            }

            if (!empty($displayName)) {
                $updateFields[] = "display_name = ?";
                $params[] = $displayName;
            }

            if (in_array($role, ['admin', 'user'])) {
                $updateFields[] = "user_type = ?";
                $params[] = ($role === 'admin') ? 'admin' : 'member';
            }

            $updateFields[] = "status = ?";
            $params[] = $isActive ? 'active' : 'inactive';

            if (!empty($password)) {
                $updateFields[] = "password = ?";
                $params[] = password_hash($password, PASSWORD_DEFAULT);
            }

            $params[] = $userId;

            $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);

            echo json_encode([
                'success' => true,
                'message' => 'User updated successfully'
            ]);
            break;

        case 'DELETE':
            // 刪除用戶
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = $input['id'] ?? 0;

            if (!$userId) {
                throw new Exception('User ID is required');
            }

            // 不能刪除自己
            if ($userId == $admin['user_id']) {
                throw new Exception('Cannot delete your own account');
            }

            // 檢查是否是最後一個管理員
            $pdo = $authDb->getPdo();
            $stmt = $pdo->prepare("SELECT COUNT(*) as admin_count FROM users WHERE user_type = 'admin' AND status = 'active'");
            $stmt->execute();
            $adminCount = $stmt->fetch()['admin_count'];

            $stmt = $pdo->prepare("SELECT user_type FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $userToDelete = $stmt->fetch();

            if ($userToDelete['user_type'] === 'admin' && $adminCount <= 1) {
                throw new Exception('Cannot delete the last admin user');
            }

            // 刪除用戶
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$userId]);

            echo json_encode([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log("Admin users API error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
