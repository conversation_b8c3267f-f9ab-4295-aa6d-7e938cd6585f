# KMS Receipt Maker - 登入系統說明

## 系統概述
這是一個簡易的檔案方式登入系統，不需要資料庫，所有用戶資料都儲存在 JSON 檔案中。

## 如何使用

### 1. 訪問系統
- 直接訪問 `index.htm` 會自動重定向到登入頁面 `login.html`
- 在登入頁面輸入用戶名和密碼
- 登入成功後會自動跳轉到主系統

### 2. 預設帳號
系統預設提供以下測試帳號：

| 用戶名 | 密碼 | 顯示名稱 | 角色 |
|--------|------|----------|------|
| admin | admin123 | Administrator | admin |
| user1 | password123 | User One | user |
| kms | kms2024 | KMS User | user |

## 如何管理用戶帳號

### 添加或修改用戶
編輯檔案：`auth/users.json`

```json
{
  "users": [
    {
      "username": "新用戶名",
      "password": "新密碼",
      "displayName": "顯示名稱",
      "role": "user"
    }
  ],
  "settings": {
    "sessionTimeout": 3600,
    "maxLoginAttempts": 5,
    "lockoutDuration": 300
  }
}
```

### 用戶屬性說明
- `username`: 登入用戶名（必填）
- `password`: 登入密碼（必填）
- `displayName`: 在系統中顯示的名稱（選填）
- `role`: 用戶角色，可以是 "admin" 或 "user"（選填）

### 系統設定說明
- `sessionTimeout`: 會話超時時間（秒），預設 3600 秒（1小時）
- `maxLoginAttempts`: 最大登入嘗試次數，預設 5 次
- `lockoutDuration`: 帳號鎖定時間（秒），預設 300 秒（5分鐘）

## 安全功能

### 1. 會話管理
- 登入後會創建會話，儲存在瀏覽器的 localStorage 中
- 會話有時效性，超時後需要重新登入
- 關閉瀏覽器或清除 localStorage 會清除會話

### 2. 登入保護
- 連續登入失敗會暫時鎖定帳號
- 鎖定期間無法嘗試登入
- 鎖定時間結束後自動解鎖

### 3. 頁面保護
- 未登入用戶無法訪問主系統
- 會話過期會自動跳轉到登入頁面

## 檔案結構

```
KMS_Receipt_Maker.app/
├── auth/
│   └── users.json          # 用戶資料檔案
├── login.html              # 登入頁面
├── js/
│   └── auth.js            # 認證系統 JavaScript
└── index.htm              # 主系統（需要登入）
```

## 常見問題

### Q: 忘記密碼怎麼辦？
A: 直接編輯 `auth/users.json` 檔案修改密碼。

### Q: 如何添加新用戶？
A: 在 `auth/users.json` 的 `users` 陣列中添加新的用戶物件。

### Q: 如何刪除用戶？
A: 從 `auth/users.json` 的 `users` 陣列中移除對應的用戶物件。

### Q: 如何修改會話超時時間？
A: 修改 `auth/users.json` 中的 `settings.sessionTimeout` 值（單位：秒）。

### Q: 帳號被鎖定了怎麼辦？
A: 等待鎖定時間結束，或者清除瀏覽器的 localStorage。

## 注意事項

1. **安全性**: 這是一個簡易的認證系統，密碼以明文儲存，不適合生產環境使用。
2. **備份**: 建議定期備份 `auth/users.json` 檔案。
3. **權限**: 確保 `auth/users.json` 檔案的讀取權限正確設置。
4. **瀏覽器相容性**: 系統使用 localStorage，需要現代瀏覽器支援。

## 技術細節

- 使用 JavaScript 的 `fetch` API 讀取用戶資料
- 會話資料儲存在瀏覽器的 localStorage 中
- 支援中英文介面切換
- 響應式設計，支援行動裝置
