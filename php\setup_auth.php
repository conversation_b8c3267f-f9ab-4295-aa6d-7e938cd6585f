<?php
/**
 * 認證系統初始化腳本
 * KMS Receipt Maker - 數據庫設置
 */

require_once 'config.php';

// 只允許從命令行或本地訪問
if (php_sapi_name() !== 'cli' && $_SERVER['REMOTE_ADDR'] !== '127.0.0.1' && $_SERVER['REMOTE_ADDR'] !== '::1') {
    http_response_code(403);
    die('Access denied');
}

try {
    // 連接數據庫
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    echo "Connected to MySQL server successfully.\n";

    // 創建數據庫（如果不存在）
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database '" . DB_NAME . "' created or already exists.\n";

    // 選擇數據庫
    $pdo->exec("USE `" . DB_NAME . "`");

    // 讀取並執行 SQL 文件
    $sqlFile = __DIR__ . '/../database/create_users_table.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }

    $sql = file_get_contents($sqlFile);
    
    // 分割 SQL 語句
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (PDOException $e) {
                echo "Warning: " . $e->getMessage() . "\n";
            }
        }
    }

    // 創建默認管理員帳號（如果不存在）
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn() > 0;

    if (!$adminExists) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password_hash, display_name, role) 
            VALUES ('admin', ?, 'Administrator', 'admin')
        ");
        $stmt->execute([$adminPassword]);
        echo "Default admin user created (username: admin, password: admin123)\n";
    } else {
        echo "Admin user already exists.\n";
    }

    // 創建測試用戶（如果不存在）
    $testUsers = [
        ['username' => 'user1', 'password' => 'password123', 'display_name' => 'User One', 'role' => 'user'],
        ['username' => 'kms', 'password' => 'kms2024', 'display_name' => 'KMS User', 'role' => 'user']
    ];

    foreach ($testUsers as $user) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$user['username']]);
        $userExists = $stmt->fetchColumn() > 0;

        if (!$userExists) {
            $passwordHash = password_hash($user['password'], PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password_hash, display_name, role) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$user['username'], $passwordHash, $user['display_name'], $user['role']]);
            echo "Test user '{$user['username']}' created (password: {$user['password']})\n";
        }
    }

    echo "\n=== Authentication System Setup Complete ===\n";
    echo "You can now:\n";
    echo "1. Access the login page: login.html\n";
    echo "2. Login with admin/admin123 to access the admin panel: admin.html\n";
    echo "3. The old auth/users.json file can be safely deleted\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
