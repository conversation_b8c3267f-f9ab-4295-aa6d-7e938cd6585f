<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Login</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo {
            font-size: 4rem;
            margin-bottom: 10px;
        }
        
        .login-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .login-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 12px 15px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .language-selector {
            text-align: center;
            margin-top: 20px;
        }
        
        .lang-btn {
            background: none;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 5px 15px;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .lang-btn:hover, .lang-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">🧾</div>
            <h2 class="login-title" data-lang="login_title">KMS Receipt Maker</h2>
            <p class="login-subtitle" data-lang="login_subtitle">Please sign in to continue</p>
        </div>
        
        <div id="loginAlert" class="alert alert-danger" style="display: none;">
            <span id="loginErrorMessage"></span>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label" data-lang="username">Username</label>
                <input type="text" class="form-control" id="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label" data-lang="password">Password</label>
                <input type="password" class="form-control" id="password" required autocomplete="current-password">
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <span class="loading-spinner" id="loadingSpinner"></span>
                <span data-lang="sign_in">Sign In</span>
            </button>
        </form>
        
        <div class="language-selector">
            <button class="lang-btn active" onclick="changeLanguage('en')" data-lang-code="en">English</button>
            <button class="lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">繁體中文</button>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/language.js"></script>
    <script>
        // Initialize language system
        document.addEventListener('DOMContentLoaded', function() {
            initializeLanguage();
        });
    </script>
</body>
</html>
