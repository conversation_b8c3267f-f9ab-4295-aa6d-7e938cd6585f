<?php
/**
 * 安全認證數據庫類
 * KMS Receipt Maker - 用戶認證系統
 */

require_once 'config.php';

class AuthDatabase {
    private $pdo;
    private $error;

    public function __construct() {
        $this->connect();
    }

    /**
     * 連接數據庫
     */
    private function connect() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            $this->error = $e->getMessage();
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }

    /**
     * 驗證用戶登入
     */
    public function authenticateUser($username, $password) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id, username, password, display_name,
                       CASE
                           WHEN user_type = 'admin' THEN 'admin'
                           ELSE 'user'
                       END as role,
                       CASE
                           WHEN status = 'active' THEN 1
                           ELSE 0
                       END as is_active,
                       login_attempts, locked_until
                FROM users
                WHERE username = ? AND status = 'active'
            ");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if (!$user) {
                return false;
            }

            // 檢查帳號是否被鎖定
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                throw new Exception('Account is locked. Please try again later.');
            }

            // 驗證密碼 - 支持舊的明文密碼和新的哈希密碼
            $passwordValid = false;
            if (password_verify($password, $user['password'])) {
                // 新的哈希密碼
                $passwordValid = true;
            } elseif ($user['password'] === $password) {
                // 舊的明文密碼，需要升級為哈希
                $passwordValid = true;
                $this->upgradePassword($user['id'], $password);
            }

            if ($passwordValid) {
                // 重置登入嘗試次數
                $this->resetLoginAttempts($user['id']);

                // 更新最後登入時間
                $this->updateLastLogin($user['id']);

                return [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'display_name' => $user['display_name'],
                    'role' => $user['role']
                ];
            } else {
                // 增加登入嘗試次數
                $this->incrementLoginAttempts($user['id']);
                return false;
            }
        } catch (Exception $e) {
            error_log("Authentication error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 創建會話
     */
    public function createSession($userId, $ipAddress = null, $userAgent = null) {
        try {
            $sessionToken = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1小時後過期

            $stmt = $this->pdo->prepare("
                INSERT INTO user_sessions (user_id, session_token, expires_at, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $sessionToken, $expiresAt, $ipAddress, $userAgent]);

            return $sessionToken;
        } catch (Exception $e) {
            error_log("Session creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 驗證會話
     */
    public function validateSession($sessionToken) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT s.user_id, u.username, u.display_name,
                       CASE
                           WHEN u.user_type = 'admin' THEN 'admin'
                           ELSE 'user'
                       END as role
                FROM user_sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? AND s.expires_at > NOW() AND u.status = 'active'
            ");
            $stmt->execute([$sessionToken]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Session validation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 刪除會話
     */
    public function deleteSession($sessionToken) {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM user_sessions WHERE session_token = ?");
            return $stmt->execute([$sessionToken]);
        } catch (Exception $e) {
            error_log("Session deletion error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 增加登入嘗試次數
     */
    private function incrementLoginAttempts($userId) {
        try {
            $maxAttempts = $this->getAuthSetting('max_login_attempts', 5);
            $lockoutDuration = $this->getAuthSetting('lockout_duration', 300);

            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET login_attempts = login_attempts + 1,
                    locked_until = CASE 
                        WHEN login_attempts + 1 >= ? 
                        THEN DATE_ADD(NOW(), INTERVAL ? SECOND)
                        ELSE locked_until 
                    END
                WHERE id = ?
            ");
            $stmt->execute([$maxAttempts, $lockoutDuration, $userId]);
        } catch (Exception $e) {
            error_log("Login attempts increment error: " . $e->getMessage());
        }
    }

    /**
     * 重置登入嘗試次數
     */
    private function resetLoginAttempts($userId) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET login_attempts = 0, locked_until = NULL 
                WHERE id = ?
            ");
            $stmt->execute([$userId]);
        } catch (Exception $e) {
            error_log("Login attempts reset error: " . $e->getMessage());
        }
    }

    /**
     * 更新最後登入時間
     */
    private function updateLastLogin($userId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$userId]);
        } catch (Exception $e) {
            error_log("Last login update error: " . $e->getMessage());
        }
    }

    /**
     * 升級明文密碼為哈希密碼
     */
    private function upgradePassword($userId, $plainPassword) {
        try {
            $hashedPassword = password_hash($plainPassword, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $stmt->execute([$hashedPassword, $userId]);
            error_log("Password upgraded to hash for user ID: $userId");
        } catch (Exception $e) {
            error_log("Password upgrade error: " . $e->getMessage());
        }
    }

    /**
     * 獲取認證設定
     */
    public function getAuthSetting($key, $default = null) {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM auth_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            return $result ? $result['setting_value'] : $default;
        } catch (Exception $e) {
            error_log("Auth setting retrieval error: " . $e->getMessage());
            return $default;
        }
    }

    /**
     * 清理過期會話
     */
    public function cleanupExpiredSessions() {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM user_sessions WHERE expires_at < NOW()");
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Session cleanup error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 獲取 PDO 實例
     */
    public function getPdo() {
        return $this->pdo;
    }

    /**
     * 獲取錯誤信息
     */
    public function getError() {
        return $this->error;
    }
}
