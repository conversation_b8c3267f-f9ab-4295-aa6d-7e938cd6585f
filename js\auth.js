/**
 * Authentication System for KMS Receipt Maker
 * Simple file-based authentication without database
 */

class AuthSystem {
    constructor() {
        this.users = [];
        this.currentUser = null;
        this.sessionKey = 'kms_auth_session';
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 5 * 60 * 1000; // 5 minutes
        this.sessionTimeout = 60 * 60 * 1000; // 1 hour

        this.initializeAuth();
    }

    async loadUsers() {
        try {
            const response = await fetch('auth/users.json');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const data = await response.json();
            this.users = data.users || [];

            // Update settings if available
            if (data.settings) {
                this.maxLoginAttempts = data.settings.maxLoginAttempts || 5;
                this.lockoutDuration = (data.settings.lockoutDuration || 300) * 1000;
                this.sessionTimeout = (data.settings.sessionTimeout || 3600) * 1000;
            }
        } catch (error) {
            console.error('Failed to load users:', error);
            // Don't show error popup, just redirect to login if not on login page
            if (!window.location.pathname.includes('login.html')) {
                this.redirectToLogin();
            }
        }
    }

    initializeAuth() {
        // Check if we're on login page
        if (window.location.pathname.includes('login.html')) {
            this.initializeLoginPage();
        } else {
            // For main pages, check authentication immediately
            // Don't wait for users to load, just check existing session
            if (!this.isAuthenticated()) {
                this.redirectToLogin();
                return;
            }
            // If authenticated, load users in background for any future operations
            this.loadUsers();
        }
    }

    initializeLoginPage() {
        // Load users data for login page
        this.loadUsers();

        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Check if already logged in
        if (this.isAuthenticated()) {
            this.redirectToMain();
        }

        // Clear any existing session on login page
        this.clearSession();
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const loginBtn = document.getElementById('loginBtn');
        const spinner = document.getElementById('loadingSpinner');

        if (!username || !password) {
            this.showError('Please enter both username and password');
            return;
        }

        // Check for lockout
        if (this.isLockedOut(username)) {
            const remainingTime = this.getRemainingLockoutTime(username);
            this.showError(`Account locked. Try again in ${Math.ceil(remainingTime / 60000)} minutes`);
            return;
        }

        // Show loading state
        loginBtn.disabled = true;
        spinner.style.display = 'inline-block';

        try {
            // Simulate network delay for better UX
            await new Promise(resolve => setTimeout(resolve, 500));

            const user = this.validateCredentials(username, password);
            
            if (user) {
                this.clearLoginAttempts(username);
                this.createSession(user);
                this.redirectToMain();
            } else {
                this.recordFailedAttempt(username);
                const attempts = this.getLoginAttempts(username);
                const remaining = this.maxLoginAttempts - attempts;
                
                if (remaining > 0) {
                    this.showError(`Invalid credentials. ${remaining} attempts remaining`);
                } else {
                    this.lockAccount(username);
                    this.showError(`Account locked for ${this.lockoutDuration / 60000} minutes`);
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('Login failed. Please try again');
        } finally {
            loginBtn.disabled = false;
            spinner.style.display = 'none';
        }
    }

    validateCredentials(username, password) {
        return this.users.find(user => 
            user.username === username && user.password === password
        );
    }

    createSession(user) {
        const session = {
            user: {
                username: user.username,
                displayName: user.displayName,
                role: user.role
            },
            loginTime: Date.now(),
            expiresAt: Date.now() + this.sessionTimeout
        };

        localStorage.setItem(this.sessionKey, JSON.stringify(session));
        this.currentUser = session.user;
    }

    isAuthenticated() {
        const session = this.getSession();
        if (!session) return false;

        // Check if session has expired
        if (Date.now() > session.expiresAt) {
            this.clearSession();
            return false;
        }

        this.currentUser = session.user;
        return true;
    }

    getSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            return sessionData ? JSON.parse(sessionData) : null;
        } catch (error) {
            console.error('Failed to parse session:', error);
            this.clearSession();
            return null;
        }
    }

    clearSession() {
        localStorage.removeItem(this.sessionKey);
        this.currentUser = null;
    }

    logout() {
        this.clearSession();
        this.redirectToLogin();
    }

    checkAuthentication() {
        if (!this.isAuthenticated()) {
            this.redirectToLogin();
        }
    }

    redirectToLogin() {
        window.location.href = 'login.html';
    }

    redirectToMain() {
        window.location.href = 'index.htm';
    }

    // Login attempt tracking
    getLoginAttempts(username) {
        const key = `login_attempts_${username}`;
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data).attempts : 0;
    }

    recordFailedAttempt(username) {
        const key = `login_attempts_${username}`;
        const attempts = this.getLoginAttempts(username) + 1;
        localStorage.setItem(key, JSON.stringify({
            attempts: attempts,
            lastAttempt: Date.now()
        }));
    }

    clearLoginAttempts(username) {
        const key = `login_attempts_${username}`;
        localStorage.removeItem(key);
    }

    isLockedOut(username) {
        const attempts = this.getLoginAttempts(username);
        if (attempts < this.maxLoginAttempts) return false;

        const key = `login_attempts_${username}`;
        const data = localStorage.getItem(key);
        if (!data) return false;

        const lastAttempt = JSON.parse(data).lastAttempt;
        return (Date.now() - lastAttempt) < this.lockoutDuration;
    }

    getRemainingLockoutTime(username) {
        const key = `login_attempts_${username}`;
        const data = localStorage.getItem(key);
        if (!data) return 0;

        const lastAttempt = JSON.parse(data).lastAttempt;
        const elapsed = Date.now() - lastAttempt;
        return Math.max(0, this.lockoutDuration - elapsed);
    }

    lockAccount(username) {
        // Account is already locked by the attempt tracking system
        console.log(`Account ${username} has been locked`);
    }

    showError(message) {
        const alertDiv = document.getElementById('loginAlert');
        const messageSpan = document.getElementById('loginErrorMessage');
        
        if (alertDiv && messageSpan) {
            messageSpan.textContent = message;
            alertDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                alertDiv.style.display = 'none';
            }, 5000);
        } else {
            alert(message);
        }
    }

    getCurrentUser() {
        return this.currentUser;
    }

    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    isAdmin() {
        return this.hasRole('admin');
    }
}

// Initialize authentication system
const authSystem = new AuthSystem();

// Global functions for easy access
window.logout = () => authSystem.logout();
window.getCurrentUser = () => authSystem.getCurrentUser();
window.isAdmin = () => authSystem.isAdmin();
