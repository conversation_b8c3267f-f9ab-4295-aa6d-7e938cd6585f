-- =====================================================
-- Update customer_name field to allow NULL values
-- KMS Receipt Maker - Database Update Script
-- =====================================================

USE `kms_receipt_maker`;

-- Update the receipts table to allow NULL customer_name
ALTER TABLE `receipts` 
MODIFY COLUMN `customer_name` varchar(100) DEFAULT NULL COMMENT 'Customer name';

-- Update any existing empty customer names to NULL
UPDATE `receipts` 
SET `customer_name` = NULL 
WHERE `customer_name` = '' OR `customer_name` = 'Guest Customer';

-- Verify the change
DESCRIBE `receipts`;
