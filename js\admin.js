/**
 * Admin Panel JavaScript
 * KMS Receipt Maker - User Management
 */

let currentEditingUser = null;

// Load all users
async function loadUsers() {
    try {
        const response = await fetch('php/admin_users.php', {
            method: 'GET',
            credentials: 'include'
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayUsers(data.users);
        } else {
            showAlert('Error loading users: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showAlert('Failed to load users', 'danger');
    }
}

// Display users in table
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        
        const statusClass = user.is_active ? 'status-active' : 'status-inactive';
        const statusText = user.is_active ? 'Active' : 'Inactive';
        
        const roleClass = user.role === 'admin' ? 'role-admin' : 'role-user';
        
        const createdDate = new Date(user.created_at).toLocaleDateString();
        const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never';
        
        const isLocked = user.locked_until && new Date(user.locked_until) > new Date();
        const lockStatus = isLocked ? ' 🔒' : '';
        
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${escapeHtml(user.username)}${lockStatus}</td>
            <td>${escapeHtml(user.display_name || '')}</td>
            <td><span class="role-badge ${roleClass}">${user.role}</span></td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>${createdDate}</td>
            <td>${lastLogin}</td>
            <td>
                <button class="btn btn-outline-primary btn-sm me-1" onclick="editUser(${user.id})" title="Edit">
                    ✏️
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="deleteUser(${user.id}, '${escapeHtml(user.username)}')" title="Delete">
                    🗑️
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Show add user modal
function showAddUserModal() {
    currentEditingUser = null;
    document.getElementById('userModalTitle').textContent = 'Add User';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('password').required = true;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// Edit user
async function editUser(userId) {
    try {
        const response = await fetch('php/admin_users.php', {
            method: 'GET',
            credentials: 'include'
        });
        
        const data = await response.json();
        
        if (data.success) {
            const user = data.users.find(u => u.id == userId);
            if (user) {
                currentEditingUser = user;
                document.getElementById('userModalTitle').textContent = 'Edit User';
                document.getElementById('userId').value = user.id;
                document.getElementById('username').value = user.username;
                document.getElementById('displayName').value = user.display_name || '';
                document.getElementById('password').value = '';
                document.getElementById('password').required = false;
                document.getElementById('role').value = user.role;
                document.getElementById('isActive').checked = user.is_active == 1;
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('userModal'));
                modal.show();
            }
        }
    } catch (error) {
        console.error('Error loading user:', error);
        showAlert('Failed to load user data', 'danger');
    }
}

// Save user (add or update)
async function saveUser() {
    const userId = document.getElementById('userId').value;
    const username = document.getElementById('username').value.trim();
    const displayName = document.getElementById('displayName').value.trim();
    const password = document.getElementById('password').value;
    const role = document.getElementById('role').value;
    const isActive = document.getElementById('isActive').checked;
    
    // Validation
    if (!username) {
        showAlert('Username is required', 'danger');
        return;
    }
    
    if (!userId && !password) {
        showAlert('Password is required for new users', 'danger');
        return;
    }
    
    if (password && password.length < 6) {
        showAlert('Password must be at least 6 characters', 'danger');
        return;
    }
    
    try {
        const method = userId ? 'PUT' : 'POST';
        const requestData = {
            username: username,
            display_name: displayName,
            role: role,
            is_active: isActive ? 1 : 0
        };
        
        if (userId) {
            requestData.id = parseInt(userId);
        }
        
        if (password) {
            requestData.password = password;
        }
        
        const response = await fetch('php/admin_users.php', {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(requestData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert(data.message, 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();
            
            // Reload users
            loadUsers();
        } else {
            showAlert('Error: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error saving user:', error);
        showAlert('Failed to save user', 'danger');
    }
}

// Delete user
async function deleteUser(userId, username) {
    if (!confirm(`Are you sure you want to delete user "${username}"?`)) {
        return;
    }
    
    try {
        const response = await fetch('php/admin_users.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({ id: userId })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert(data.message, 'success');
            loadUsers();
        } else {
            showAlert('Error: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showAlert('Failed to delete user', 'danger');
    }
}

// Show alert message
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.innerHTML = alertHtml;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            const alert = bootstrap.Alert.getInstance(alertElement);
            if (alert) {
                alert.close();
            }
        }
    }, 5000);
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Bootstrap Modal polyfill for basic functionality
if (typeof bootstrap === 'undefined') {
    window.bootstrap = {
        Modal: function(element) {
            this.element = element;
            this.show = function() {
                element.style.display = 'block';
                element.classList.add('show');
                document.body.classList.add('modal-open');
            };
            this.hide = function() {
                element.style.display = 'none';
                element.classList.remove('show');
                document.body.classList.remove('modal-open');
            };
        },
        Alert: {
            getInstance: function(element) {
                return {
                    close: function() {
                        element.remove();
                    }
                };
            }
        }
    };
    
    bootstrap.Modal.getInstance = function(element) {
        return new bootstrap.Modal(element);
    };
}
