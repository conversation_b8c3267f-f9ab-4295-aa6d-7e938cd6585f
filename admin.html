<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Admin Panel</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .user-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .user-table table {
            margin: 0;
        }
        
        .user-table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #333;
        }
        
        .user-table td {
            border: none;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .role-admin {
            background: #fff3cd;
            color: #856404;
        }
        
        .role-user {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 0.8rem;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            border-bottom: 1px solid #eee;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e1e5e9;
            padding: 10px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">🛡️ Admin Panel</h1>
                    <p class="mb-0">User Management System</p>
                </div>
                <div>
                    <span id="currentAdminDisplay" class="me-3"></span>
                    <a href="index.htm" class="btn btn-light btn-sm me-2">
                        <span class="icon-symbol">🏠</span> Main System
                    </a>
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="logout()">
                        <span class="icon-symbol">🚪</span> Logout
                    </button>
                </div>
            </div>
        </div>

        <div id="alertContainer"></div>

        <div class="user-table">
            <div class="p-3 border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">👥 User Management</h5>
                    <button type="button" class="btn btn-primary" onclick="showAddUserModal()">
                        <span class="icon-symbol">➕</span> Add User
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Display Name</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">Add User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId" value="">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="displayName" class="form-label">Display Name</label>
                            <input type="text" class="form-control" id="displayName">
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" required>
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role">
                                <option value="user">User</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" checked>
                                <label class="form-check-label" for="isActive">
                                    Active
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">Save User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/bootstrap-replacement.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/language.js"></script>
    <script src="js/admin.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin access
            if (!isAdmin()) {
                alert('Admin access required');
                window.location.href = 'index.htm';
                return;
            }
            
            // Display current admin info
            const currentUser = getCurrentUser();
            if (currentUser) {
                document.getElementById('currentAdminDisplay').textContent = 
                    `Welcome, ${currentUser.display_name || currentUser.username}`;
            }
            
            // Load users
            loadUsers();
        });
    </script>
</body>
</html>
